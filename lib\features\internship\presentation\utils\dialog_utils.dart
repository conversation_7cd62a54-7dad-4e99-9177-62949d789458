/// -----
/// dialog_utils.dart
///
/// 对话框工具类
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:city_pickers/city_pickers.dart';
import 'package:intl/intl.dart';

/// 对话框工具类
class DialogUtils {
  /// 显示下拉选择对话框（底部弹出式）
  static void showDropdownDialog(
    BuildContext context, {
    required String title,
    required List<String> items,
    required Function(String) onSelected,
  }) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ),
          ),

          // 选项列表
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      title: Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontSize: 30.sp,
                            color: AppTheme.black333,
                          ),
                        ),
                      ),
                      onTap: () {
                        onSelected(items[index]);
                        Navigator.pop(context);
                      },
                    ),
                    Divider(
                      height: 1.h,
                      thickness: 1.h,
                      color: AppTheme.dividerColor,
                    ),
                  ],
                );
              },
            ),
          ),

          // 取消按钮
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 3,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示省市区选择器
  static Future<void> showLocationPicker(
    BuildContext context, {
    required String title,
    required Function(String) onSelected,
  }) async {
    Result? result = await CityPickers.showCityPicker(
      context: context,
      height: 300,
      cancelWidget: Text(
        '取消',
        style: TextStyle(color: Colors.grey[600]),
      ),
      confirmWidget: const Text(
        '确定',
        style: TextStyle(color: AppTheme.primaryColor),
      ),
      itemExtent: 40, // 项目高度
    );

    if (result != null) {
      // 组合省市区字符串
      String selectedLocation = '${result.provinceName}/${result.cityName}/${result.areaName}';
      onSelected(selectedLocation);
    }
  }

  /// 显示日期选择器
  static Future<void> showDatePicker(
    BuildContext context, {
    required String title,
    required Function(String) onSelected,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime.now().subtract(const Duration(days: 365)),
      lastDate: lastDate ?? DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      final formattedDate = DateFormat('yyyy-MM-dd').format(picked);
      onSelected(formattedDate);
    }
  }
}
